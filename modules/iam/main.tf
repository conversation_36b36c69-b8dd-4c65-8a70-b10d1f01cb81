//apphero lambda exex role
resource "aws_iam_role" "lambda_exec_role" {
  name = "apphero-lambda-exec-role-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      },
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          AWS = var.s3_role_arn
        }
      },
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          Service = "ses.amazonaws.com"
        }
      },
      {
        Effect = "Allow",
        Principal = {
          Service = "cognito-idp.amazonaws.com"
        },
        Action = "sts:AssumeRole"
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_iam_policy" "lambda_exec_policy" {
  name        = "apphero-lambda-exec-policy-${var.environment}"
  description = "gus lambda exec policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "logs:DescribeLogStreams"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Action = [
          "xray:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
       {
        Action = [
          "ssm:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Action = [
          "s3:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Action = [
          "appsync:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Action = [
          "acm:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Action = [
          "apigateway:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Action = [
          "lambda:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Action = [
          "lambda:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Action = [
          "dynamodb:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Action = [
          "secretsmanager:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Effect = "Allow"
        Action = [
          "sts:AssumeRole"
        ]
        Resource : "*"
      },
      {
        Effect = "Allow",
        Action = [
          "ses:*"
        ],
        Resource = "*"
      },
      {
        Effect = "Allow",
        Action = [
          "cognito-idp:*"
        ],
        Resource = "*"
      },
            {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "route53domains:*"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "route53:*"
        ]
      },
      {
        Effect = "Allow",
        Action = [
          "SNS:*",
          "sqs:*"
        ],
        Resource = "*"
      },
      {
        Effect = "Allow",
        Action = [
          "sts:AssumeRole"
        ],
        Resource = "arn:aws:iam::${var.accountId}:role/apphero-appsync-graphQL-${var.environment}"
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_iam_policy_attachment" "lambda_exec_policy_attachment" {
  name       = "apphero-lambda-exec-policy-attachment-${var.environment}"
  policy_arn = aws_iam_policy.lambda_exec_policy.arn
  roles      = [aws_iam_role.lambda_exec_role.name]
}

//notification role
resource "aws_iam_role" "apphero_appsync_graphQl_role" {
  name = "apphero-appsync-graphQL-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "appsync.amazonaws.com"
        }
      },
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          AWS = "arn:aws:iam::${var.accountId}:role/apphero-notify-listener-role-${var.environment}"
        }
      },
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          AWS = "arn:aws:iam::${var.accountId}:role/apphero-lambda-exec-role-${var.environment}"
        }
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_iam_policy" "apphero_appsync_graphQl_policy" {
  name        = "apphero-appsync-graphQl-${var.environment}"
  description = "apphero graphQl policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "appsync:GraphQL"
        ]
        Resource = "*"
        Effect   = "Allow"
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_iam_policy_attachment" "apphero_appsync_graphql_policy_attachment" {
  name       = "apphero-graphql-policy-attachment-${var.environment}"
  policy_arn = aws_iam_policy.apphero_appsync_graphQl_policy.arn
  roles      = [aws_iam_role.apphero_appsync_graphQl_role.name]
}

#codepipeline access role
resource "aws_iam_role" "apphero_codepipeline_access_role" {
  name = "apphero-codepipeline-access-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "codepipeline.amazonaws.com"
        }
      },
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

data "aws_iam_policy_document" "apphero_codepipeline_access_policy" {
  statement {
    effect = "Allow"

    actions = [
      "s3:GetObject",
      "s3:GetObjectVersion",
      "s3:GetBucketVersioning",
      "s3:PutObjectAcl",
      "s3:PutObject",
    ]

    resources = ["*"]
  }

  statement {
    effect    = "Allow"
    actions   = ["codestar-connections:UseConnection"]
    resources = ["*"]
  }

  statement {
    effect = "Allow"
    actions = [
      "codecommit:CancelUploadArchive",
      "codecommit:GetBranch",
      "codecommit:GetCommit",
      "codecommit:GetRepository",
      "codecommit:GetUploadArchiveStatus",
      "codecommit:UploadArchive"
    ]
    resources = [
      "arn:aws:codecommit:${var.region}:${var.accountId}:r3-oaf-backend",
      "arn:aws:codecommit:${var.region}:${var.accountId}:gus-middleware-service",
      "arn:aws:codecommit:${var.region}:${var.accountId}:gus-eip-infra",
      "arn:aws:codecommit:${var.region}:${var.accountId}:r3-pdf-generator",
      "arn:aws:codecommit:${var.region}:${var.accountId}:r3-oaf-frontend",
      "arn:aws:codecommit:${var.region}:${var.accountId}:gus-apphero-frontend",
      "arn:aws:codecommit:${var.region}:${var.accountId}:apphero-backend-service",
      "arn:aws:codecommit:${var.region}:${var.accountId}:gus-student-detail-oaf-frontend",
      "arn:aws:codecommit:${var.region}:${var.accountId}:gus-student-detail-backend-service",
      "arn:aws:codecommit:${var.region}:${var.accountId}:apphero-sf-sync-service"
    ]
  }

  statement {
    effect = "Allow"

    actions = [
      "cloudwatch:*"
    ]
    resources = ["*"]
  }

  statement {
    effect = "Allow"

    actions = [
      "codebuild:*"
    ]
    resources = ["*"]
  }

  statement {
    effect = "Allow"

    actions = [
      "cloudfront:GetDistribution",
      "cloudfront:GetInvalidation",
      "cloudfront:CreateInvalidation"
    ]
    resources = ["*"]
  }
}

resource "aws_iam_role_policy" "apphero_codepipeline_policy" {
  name   = "apphero-codepipeline-access-${var.environment}"
  role   = aws_iam_role.apphero_codepipeline_access_role.id
  policy = data.aws_iam_policy_document.apphero_codepipeline_access_policy.json
}


# codebuild access role
resource "aws_iam_role" "apphero_codebuild_access_role" {
  name = "apphero-codebuild-access-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "codebuild.amazonaws.com"
        }
      },
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_iam_policy" "apphero_codebuild_access_policy" {
  name = "apphero-codebuild-access-${var.environment}"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement : [
      {
        Effect : "Allow",
        Resource : [
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/gus-middleware-service-${var.environment}",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/gus-middleware-service-${var.environment}:*",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/${var.environment}-gus-middleware-service",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/${var.environment}-gus-middleware-service:*",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/r3-pdf-generator-${var.environment}:*",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/r3-pdf-generator-${var.environment}",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/r3-oaf-backend-${var.environment}",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/r3-oaf-backend-${var.environment}:*",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/r3-oaf-backend",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/r3-oaf-backend:*",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/r3-oaf-frontend-${var.environment}",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/r3-oaf-frontend-${var.environment}:*",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/gus-apphero-frontend-${var.environment}",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/gus-apphero-frontend-${var.environment}:*",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/apphero-backend-service-${var.environment}",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/gus-student-detail-oaf-frontend-${var.environment}:*",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/gus-student-detail-oaf-frontend-${var.environment}",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/gus-student-detail-backend-service-${var.environment}:*",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/gus-student-detail-backend-service-${var.environment}",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/apphero-sf-sync-service-${var.environment}:*",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/apphero-sf-sync-service-${var.environment}"
        ],
        Action : [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "s3:*"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "athena:CreateWorkGroup"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "logs:*"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "lambda:*"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "appsync:*"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "cloudfront:*"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "apigateway:*"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "route53domains:*"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "route53:*"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "acm:*"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "cloudwatch:*"
        ]
      },
      {
        Effect : "Allow",
        Action : [
          "codebuild:CreateReportGroup",
          "codebuild:CreateReport",
          "codebuild:UpdateReport",
          "codebuild:BatchPutTestCases",
          "codebuild:BatchPutCodeCoverages",
          "codebuild:StartBuild",
          "codebuild:BatchGetBuilds",
          "codebuild:BatchGetProjects",
          "codebuild:CreateProject",
          "codebuild:ListProjects",
          "codebuild:BatchPutTestCases",
          "codebuild:BatchPutCodeCoverages",
          "codebuild:StopBuild",
          "codebuild:RetryBuild"
        ],
        Resource : [
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/r3-oaf-backend-${var.environment}",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/gus-middleware-service-${var.environment}",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/${var.environment}-gus-middleware-service",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/r3-pdf-generation-${var.environment}",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/r3-oaf-backend",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/r3-oaf-frontend-${var.environment}",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/r3-oaf-frontend-${var.environment}:*",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/gus-apphero-frontend-${var.environment}",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/gus-apphero-frontend-${var.environment}:*",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/r3-oaf-backend-${var.environment}",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/apphero-backend-service-${var.environment}",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/apphero-backend-service-${var.environment}:*",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/gus-student-detail-oaf-frontend-${var.environment}:*",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/gus-student-detail-oaf-frontend-${var.environment}",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/apphero-sf-sync-service-${var.environment}:*",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/apphero-sf-sync-service-${var.environment}"
        ]
      },
      {
        Effect : "Allow",
        Action : [
          "dynamodb:*"
        ],
        Resource : [
          "arn:aws:dynamodb:${var.region}:${var.accountId}:table/*"
        ]
      },
      {
        Effect : "Allow",
        Action : [
          "cloudformation:DescribeStacks",
          "cloudformation:DescribeStackResource",
          "cloudformation:ValidateTemplate",
          "cloudformation:DeleteChangeSet",
          "cloudformation:CreateChangeSet",
          "cloudformation:DescribeChangeSet",
          "cloudformation:ExecuteChangeSet",
          "cloudformation:DescribeStackEvents",
          "cloudformation:*"
        ],
        Resource : [
          "*"
        ]
      },
      {
        Effect : "Allow",
        Action : [
          "glue:CreateDatabase",
          "glue:BatchGetCrawlers",
          "glue:ListCrawlers",
          "glue:GetTables",
          "glue:GetDatabase",
          "glue:TagResource",
          "glue:UpdateDatabase",
          "glue:CreateTable",
          "glue:UpdateTable",
          "glue:GetTags",
          "glue:DeleteDatabase",
          "glue:DeleteTable",
          "glue:GetTable"
        ],
        Resource : [
          "*"
        ]
      },
      {
        Effect : "Allow",
        Action : [
          "iam:CreatePolicy",
          "iam:AttachRolePolicy",
          "iam:DeletePolicy",
          "iam:GetPolicy",
          "iam:ListPolicies",
          "iam:CreateRole",
          "iam:GetPolicy",
          "iam:GetRole",
          "iam:ListPolicies",
          "iam:ListRoles",
          "iam:UpdateRole",
          "iam:PutRolePolicy",
          "iam:GetRole",
          "iam:PassRole",
          "iam:ListRolePolicies",
          "iam:GetPolicyVersion",
          "iam:ListAttachedRolePolicies",
          "iam:ListPolicyVersions",
          "iam:ListInstanceProfilesForRole",
          "iam:DeleteRole",
          "iam:ListEntitiesForPolicy",
          "iam:GetRolePolicy",
          "iam:ListEntitiesForPolicy",
          "iam:DetachRolePolicy",
          "iam:DeleteRolePolicy",
          "iam:TagRole",
          "iam:CreateServiceLinkedRole"
        ],
        Resource : [
          "*"
        ]
      },
      {
        Effect : "Allow",
        Action : [
          "ecr:*"
        ],
        Resource : [
          "*"
        ]
      },
      {
        Effect : "Allow",
        Action : [
          "events:PutRule",
          "events:DescribeRule",
          "events:ListTagsForResource",
          "events:DeleteRule",
          "events:PutTargets",
          "events:ListTargetsByRule",
          "events:RemoveTargets"
        ],
        Resource : [
          "*"
        ]
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_iam_policy_attachment" "apphero_codebuild_access_policy_attachment" {
  name       = "apphero-codebuild-access-policy-attachment-${var.environment}"
  policy_arn = aws_iam_policy.apphero_codebuild_access_policy.arn
  roles      = [aws_iam_role.apphero_codebuild_access_role.name]
}

resource "aws_iam_policy" "apphero_codebuild_sf_sync_sns_access_policy" {
  name = "apphero-codebuild-sf-sync-sns-access-${var.environment}"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement : [
      {  
        Action: [  
            "sns:Subscribe",  
            "sns:ConfirmSubscription",  
            "sns:Publish",  
            "sns:Unsubscribe",  
            "sns:TagResource",  
            "sns:ListTagsForResource"  
        ],  
        Effect: "Allow",  
        Resource: [ 
              "arn:aws:sns:${var.region}:${var.accountId}:apphero-mailer-events-tracker-${var.environment}"
        ]  
        }]
      })
    tags = {
      Environment = var.environment_tag
      PROJECT     = "APPHERO"
      TEAM        = "EIP Development Team"
    }
}

resource "aws_iam_policy_attachment" "apphero_codebuild_sf_sync_sns_access_policy_attachment" {
  name       = "apphero-codebuild-sf-sync-sns-access-policy-attachment-${var.environment}"
  policy_arn = aws_iam_policy.apphero_codebuild_sf_sync_sns_access_policy.arn
  roles      = [aws_iam_role.apphero_codebuild_access_role.name]
}

# codepipeline start role
resource "aws_iam_role" "apphero_codepipeline_start_access" {
  name = "apphero-codepipeline-start-access-${var.environment}"

  assume_role_policy = jsonencode({
    Version : "2012-10-17",
    Statement : [
      {
        Effect : "Allow",
        Principal : {
          Service : "events.amazonaws.com"
        },
        Action : "sts:AssumeRole"
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_iam_policy" "apphero_codepipeline_start_policy" {
  name = "apphero-codepipeline-start-access-${var.environment}"

  policy = jsonencode({
    Version : "2012-10-17",
    Statement : [
      {
        Effect : "Allow",
        Action : [
          "codepipeline:StartPipelineExecution"
        ],
        Resource : [
          "arn:aws:codepipeline:${var.region}:${var.accountId}:apphero-backend-service-${var.environment}",
          "arn:aws:codepipeline:${var.region}:${var.accountId}:gus-apphero-frontend-${var.environment}",
          "arn:aws:codepipeline:${var.region}:${var.accountId}:gus-student-detail-oaf-frontend-${var.environment}",
          "arn:aws:codepipeline:${var.region}:${var.accountId}:gus-student-detail-backend-service-${var.environment}",
          "arn:aws:codepipeline:${var.region}:${var.accountId}:apphero-sf-sync-service-${var.environment}"
        ]
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_iam_policy_attachment" "apphero_codepipeline_start_access_policy_attachment" {
  name       = "apphero-codepipeline-start-access-policy-attachment-${var.environment}"
  policy_arn = aws_iam_policy.apphero_codepipeline_start_policy.arn
  roles      = [aws_iam_role.apphero_codepipeline_start_access.name]
}
