Resources:
  AgentOapOAuthTokens:
    Type: AWS::SSM::Parameter
    Properties:
      Name: !Sub '/apphero/${self:provider.stage}/agent-oap-oauth-tokens'
      Type: String
      Value: 'PLACEHOLDER_BASE64_ENCODED_CLIENTID_CLIENTSECRET' # Replace with real base64 value post-deployment
      Description: OAuth credentials for authentication - stores Base64 encoded client_id:client_secret for Basic auth
      Tags:
        Environment: ${self:provider.stage}
        Team: EIP Development Team
        Project: APPHERO
